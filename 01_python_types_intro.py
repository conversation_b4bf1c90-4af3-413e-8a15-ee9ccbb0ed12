# Python 类型提示入门 - FastAPI 学习准备
"""
这个文件包含了 Python 类型提示的基础知识，
这些知识对于学习 FastAPI 至关重要。

FastAPI 大量使用 Python 的类型提示来：
1. 自动生成 API 文档
2. 数据验证
3. 序列化和反序列化
4. 编辑器支持和代码补全
"""

from typing import Union, List, Dict, Optional, Any
from datetime import datetime
from enum import Enum

# ============================================================================
# 1. 基本类型提示
# ============================================================================

def greet(name: str) -> str:
    """基本的字符串类型提示"""
    return f"Hello, {name}!"

def add_numbers(a: int, b: int) -> int:
    """整数类型提示"""
    return a + b

def calculate_average(numbers: List[float]) -> float:
    """列表和浮点数类型提示"""
    return sum(numbers) / len(numbers)

def is_adult(age: int) -> bool:
    """布尔类型提示"""
    return age >= 18

# ============================================================================
# 2. 复合类型
# ============================================================================

def process_user_data(user_data: Dict[str, Any]) -> Dict[str, str]:
    """字典类型提示"""
    return {
        "name": str(user_data.get("name", "")),
        "email": str(user_data.get("email", "")),
        "status": "processed"
    }

def get_user_names(users: List[Dict[str, str]]) -> List[str]:
    """嵌套类型提示"""
    return [user["name"] for user in users]

# ============================================================================
# 3. Optional 和 Union 类型
# ============================================================================

def find_user(user_id: int) -> Optional[Dict[str, str]]:
    """Optional 表示可能返回 None"""
    # 模拟数据库查询
    if user_id == 1:
        return {"name": "Alice", "email": "<EMAIL>"}
    return None

def process_id(user_id: Union[int, str]) -> str:
    """Union 表示可以是多种类型之一"""
    return str(user_id)

# Python 3.10+ 的新语法（推荐）
def find_user_new(user_id: int) -> Dict[str, str] | None:
    """使用 | 语法代替 Union"""
    if user_id == 1:
        return {"name": "Alice", "email": "<EMAIL>"}
    return None

def process_id_new(user_id: int | str) -> str:
    """使用 | 语法代替 Union"""
    return str(user_id)

# ============================================================================
# 4. 类和数据类
# ============================================================================

class User:
    """基本用户类"""
    def __init__(self, name: str, email: str, age: int):
        self.name = name
        self.email = email
        self.age = age
    
    def get_info(self) -> Dict[str, Union[str, int]]:
        return {
            "name": self.name,
            "email": self.email,
            "age": self.age
        }

# 使用 dataclass（推荐用于数据结构）
from dataclasses import dataclass

@dataclass
class Product:
    """产品数据类"""
    id: int
    name: str
    price: float
    in_stock: bool = True
    
    def get_display_price(self) -> str:
        return f"${self.price:.2f}"

# ============================================================================
# 5. 枚举类型
# ============================================================================

class Status(str, Enum):
    """状态枚举 - 继承 str 使其可以直接序列化"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

def update_user_status(user_id: int, status: Status) -> Dict[str, str]:
    """使用枚举类型"""
    return {
        "user_id": str(user_id),
        "status": status.value,
        "updated_at": datetime.now().isoformat()
    }

# ============================================================================
# 6. 泛型和类型变量
# ============================================================================

from typing import TypeVar, Generic

T = TypeVar('T')

class Container(Generic[T]):
    """泛型容器类"""
    def __init__(self, item: T):
        self.item = item
    
    def get_item(self) -> T:
        return self.item

def create_list(item: T, count: int) -> List[T]:
    """泛型函数"""
    return [item] * count

# ============================================================================
# 7. 实际应用示例
# ============================================================================

# 模拟 API 响应数据结构
@dataclass
class APIResponse:
    """API 响应数据结构"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

def create_user_response(user: User) -> APIResponse:
    """创建用户响应"""
    return APIResponse(
        success=True,
        data=user.get_info()
    )

def create_error_response(error_message: str) -> APIResponse:
    """创建错误响应"""
    return APIResponse(
        success=False,
        error=error_message
    )

# ============================================================================
# 8. 练习函数
# ============================================================================

def exercise_1():
    """练习 1: 基本类型提示"""
    print("=== 练习 1: 基本类型提示 ===")
    
    # 测试基本函数
    print(greet("FastAPI"))
    print(f"2 + 3 = {add_numbers(2, 3)}")
    print(f"平均值: {calculate_average([1.0, 2.0, 3.0, 4.0, 5.0])}")
    print(f"是否成年: {is_adult(20)}")

def exercise_2():
    """练习 2: 复合类型"""
    print("\n=== 练习 2: 复合类型 ===")
    
    user_data = {"name": "Alice", "email": "<EMAIL>", "age": 25}
    processed = process_user_data(user_data)
    print(f"处理后的用户数据: {processed}")
    
    users = [
        {"name": "Alice", "email": "<EMAIL>"},
        {"name": "Bob", "email": "<EMAIL>"}
    ]
    names = get_user_names(users)
    print(f"用户名列表: {names}")

def exercise_3():
    """练习 3: Optional 和 Union"""
    print("\n=== 练习 3: Optional 和 Union ===")
    
    user = find_user(1)
    print(f"找到用户: {user}")
    
    user_not_found = find_user(999)
    print(f"未找到用户: {user_not_found}")
    
    print(f"处理整数 ID: {process_id(123)}")
    print(f"处理字符串 ID: {process_id('abc123')}")

def exercise_4():
    """练习 4: 类和枚举"""
    print("\n=== 练习 4: 类和枚举 ===")
    
    # 创建用户
    user = User("Alice", "<EMAIL>", 25)
    print(f"用户信息: {user.get_info()}")
    
    # 创建产品
    product = Product(1, "笔记本电脑", 999.99)
    print(f"产品: {product.name}, 价格: {product.get_display_price()}")
    
    # 使用枚举
    status_update = update_user_status(1, Status.ACTIVE)
    print(f"状态更新: {status_update}")

def exercise_5():
    """练习 5: API 响应示例"""
    print("\n=== 练习 5: API 响应示例 ===")
    
    user = User("Bob", "<EMAIL>", 30)
    success_response = create_user_response(user)
    print(f"成功响应: {success_response}")
    
    error_response = create_error_response("用户不存在")
    print(f"错误响应: {error_response}")

if __name__ == "__main__":
    print("Python 类型提示学习和练习")
    print("=" * 50)
    
    exercise_1()
    exercise_2()
    exercise_3()
    exercise_4()
    exercise_5()
    
    print("\n" + "=" * 50)
    print("类型提示学习完成！")
    print("接下来可以学习异步编程概念。")
