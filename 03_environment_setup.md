# FastAPI 学习环境设置指南

## 📋 目录
1. [系统要求](#系统要求)
2. [Python 环境设置](#python-环境设置)
3. [虚拟环境创建](#虚拟环境创建)
4. [FastAPI 安装](#fastapi-安装)
5. [开发工具配置](#开发工具配置)
6. [环境变量管理](#环境变量管理)
7. [验证安装](#验证安装)

## 🔧 系统要求

### 最低要求
- **Python**: 3.8 或更高版本（推荐 3.11+）
- **操作系统**: Windows 10+, macOS 10.14+, 或 Linux
- **内存**: 至少 4GB RAM
- **存储**: 至少 2GB 可用空间

### 推荐配置
- **Python**: 3.11 或 3.12（最佳性能）
- **内存**: 8GB+ RAM
- **编辑器**: VS Code, PyCharm, 或其他支持 Python 的 IDE

## 🐍 Python 环境设置

### 检查 Python 版本
```bash
python --version
# 或
python3 --version
```

### 安装 Python（如果需要）

#### Windows
1. 访问 [python.org](https://www.python.org/downloads/)
2. 下载最新版本的 Python
3. 运行安装程序，**确保勾选 "Add Python to PATH"**

#### macOS
```bash
# 使用 Homebrew（推荐）
brew install python

# 或使用 pyenv
brew install pyenv
pyenv install 3.11.0
pyenv global 3.11.0
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

## 📦 虚拟环境创建

### 为什么需要虚拟环境？
- 隔离项目依赖
- 避免版本冲突
- 便于项目管理
- 易于部署

### 创建虚拟环境

#### 方法 1: 使用 venv（推荐）
```bash
# 创建虚拟环境
python -m venv fastapi-learning

# 激活虚拟环境
# Windows
fastapi-learning\Scripts\activate

# macOS/Linux
source fastapi-learning/bin/activate

# 验证激活
which python  # 应该显示虚拟环境中的 Python 路径
```

#### 方法 2: 使用 conda
```bash
# 创建环境
conda create -n fastapi-learning python=3.11

# 激活环境
conda activate fastapi-learning
```

### 虚拟环境管理
```bash
# 停用虚拟环境
deactivate

# 删除虚拟环境（在停用状态下）
rm -rf fastapi-learning  # Linux/macOS
rmdir /s fastapi-learning  # Windows
```

## 🚀 FastAPI 安装

### 标准安装（推荐）
```bash
# 确保虚拟环境已激活
pip install "fastapi[standard]"
```

这将安装：
- FastAPI 核心
- Uvicorn（ASGI 服务器）
- Pydantic（数据验证）
- 其他标准依赖

### 最小安装
```bash
# 仅安装核心组件
pip install fastapi

# 单独安装服务器
pip install uvicorn
```

### 开发依赖安装
```bash
# 测试工具
pip install pytest pytest-asyncio httpx

# 代码格式化
pip install black isort

# 类型检查
pip install mypy

# 文档生成
pip install mkdocs mkdocs-material
```

### 数据库相关（可选）
```bash
# SQLAlchemy ORM
pip install sqlalchemy

# 数据库驱动
pip install psycopg2-binary  # PostgreSQL
pip install pymysql          # MySQL
pip install aiosqlite        # SQLite (异步)
```

## 🛠️ 开发工具配置

### VS Code 配置

#### 推荐扩展
- Python
- Pylance
- Python Docstring Generator
- REST Client
- Thunder Client

#### 设置文件 (.vscode/settings.json)
```json
{
    "python.defaultInterpreterPath": "./fastapi-learning/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

### PyCharm 配置
1. 打开项目
2. File → Settings → Project → Python Interpreter
3. 选择虚拟环境中的 Python 解释器
4. 启用代码检查和格式化

## 🌍 环境变量管理

### 创建 .env 文件
```bash
# .env
DATABASE_URL=sqlite:///./test.db
SECRET_KEY=your-secret-key-here
DEBUG=True
API_HOST=127.0.0.1
API_PORT=8000
```

### 安装 python-dotenv
```bash
pip install python-dotenv
```

### 使用环境变量
```python
# config.py
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./default.db")
SECRET_KEY = os.getenv("SECRET_KEY", "default-secret-key")
DEBUG = os.getenv("DEBUG", "False").lower() == "true"
API_HOST = os.getenv("API_HOST", "127.0.0.1")
API_PORT = int(os.getenv("API_PORT", "8000"))
```

## ✅ 验证安装

### 创建测试文件
```python
# test_installation.py
from fastapi import FastAPI
import uvicorn

app = FastAPI(title="安装测试", version="1.0.0")

@app.get("/")
async def root():
    return {"message": "FastAPI 安装成功！"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "framework": "FastAPI"}

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000)
```

### 运行测试
```bash
# 方法 1: 直接运行
python test_installation.py

# 方法 2: 使用 uvicorn
uvicorn test_installation:app --reload

# 方法 3: 使用 FastAPI CLI
fastapi dev test_installation.py
```

### 验证步骤
1. 打开浏览器访问 `http://127.0.0.1:8000`
2. 应该看到 JSON 响应：`{"message": "FastAPI 安装成功！"}`
3. 访问 `http://127.0.0.1:8000/docs` 查看自动生成的 API 文档
4. 访问 `http://127.0.0.1:8000/redoc` 查看 ReDoc 文档

## 📝 requirements.txt 文件

### 生成依赖文件
```bash
pip freeze > requirements.txt
```

### 示例 requirements.txt
```txt
fastapi[standard]==0.104.1
uvicorn[standard]==0.24.0
python-dotenv==1.0.0
sqlalchemy==2.0.23
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
black==23.11.0
isort==5.12.0
mypy==1.7.1
```

### 从 requirements.txt 安装
```bash
pip install -r requirements.txt
```

## 🔍 常见问题解决

### 问题 1: Python 版本过低
```bash
# 错误信息: Python 3.7 不支持
# 解决方案: 升级到 Python 3.8+
```

### 问题 2: 虚拟环境激活失败
```bash
# Windows PowerShell 执行策略问题
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 问题 3: 端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000  # Windows
lsof -i :8000                 # macOS/Linux

# 使用不同端口
uvicorn main:app --port 8001
```

### 问题 4: 导入错误
```bash
# 确保在正确的目录和虚拟环境中
pwd
which python
pip list
```

## 🎯 下一步

环境设置完成后，您可以：

1. ✅ 运行第一个 FastAPI 应用
2. ✅ 学习 FastAPI 核心概念
3. ✅ 开始构建 API 端点
4. ✅ 探索自动文档生成功能

## 📚 有用的资源

- [FastAPI 官方文档](https://fastapi.tiangolo.com/)
- [Python 虚拟环境指南](https://docs.python.org/3/tutorial/venv.html)
- [Uvicorn 文档](https://www.uvicorn.org/)
- [Pydantic 文档](https://docs.pydantic.dev/)

---

**恭喜！** 您的 FastAPI 学习环境已经准备就绪。现在可以开始学习 FastAPI 的核心概念了！
