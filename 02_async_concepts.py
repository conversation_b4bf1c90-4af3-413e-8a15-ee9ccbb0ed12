# 异步编程概念 - FastAPI 学习准备
"""
这个文件介绍了 Python 异步编程的核心概念，
这些概念对于理解 FastAPI 的异步特性至关重要。

FastAPI 支持异步编程，可以显著提高 I/O 密集型应用的性能。
"""

import asyncio
import time
import aiohttp
import aiofiles
from typing import List, Dict, Any
from datetime import datetime

# ============================================================================
# 1. 同步 vs 异步对比
# ============================================================================

def sync_task(name: str, duration: int) -> str:
    """同步任务示例"""
    print(f"开始同步任务 {name}")
    time.sleep(duration)  # 模拟耗时操作
    print(f"完成同步任务 {name}")
    return f"任务 {name} 完成"

async def async_task(name: str, duration: int) -> str:
    """异步任务示例"""
    print(f"开始异步任务 {name}")
    await asyncio.sleep(duration)  # 异步等待
    print(f"完成异步任务 {name}")
    return f"任务 {name} 完成"

def run_sync_tasks():
    """运行同步任务"""
    print("=== 同步执行 ===")
    start_time = time.time()
    
    results = []
    for i in range(3):
        result = sync_task(f"sync-{i}", 1)
        results.append(result)
    
    end_time = time.time()
    print(f"同步执行总时间: {end_time - start_time:.2f} 秒")
    return results

async def run_async_tasks():
    """运行异步任务"""
    print("\n=== 异步执行 ===")
    start_time = time.time()
    
    # 并发执行多个异步任务
    tasks = [async_task(f"async-{i}", 1) for i in range(3)]
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    print(f"异步执行总时间: {end_time - start_time:.2f} 秒")
    return results

# ============================================================================
# 2. 异步函数的基本语法
# ============================================================================

async def simple_async_function() -> str:
    """简单的异步函数"""
    await asyncio.sleep(0.1)
    return "Hello, Async World!"

async def async_function_with_params(name: str, delay: float = 0.1) -> Dict[str, Any]:
    """带参数的异步函数"""
    start_time = datetime.now()
    await asyncio.sleep(delay)
    end_time = datetime.now()
    
    return {
        "name": name,
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "duration": delay
    }

# ============================================================================
# 3. 异步上下文管理器
# ============================================================================

class AsyncDatabaseConnection:
    """模拟异步数据库连接"""
    
    def __init__(self, db_name: str):
        self.db_name = db_name
        self.connected = False
    
    async def __aenter__(self):
        """异步进入上下文"""
        print(f"连接到数据库 {self.db_name}")
        await asyncio.sleep(0.1)  # 模拟连接时间
        self.connected = True
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步退出上下文"""
        print(f"断开数据库 {self.db_name} 连接")
        await asyncio.sleep(0.1)  # 模拟断开时间
        self.connected = False
    
    async def query(self, sql: str) -> List[Dict[str, Any]]:
        """模拟异步查询"""
        if not self.connected:
            raise RuntimeError("数据库未连接")
        
        print(f"执行查询: {sql}")
        await asyncio.sleep(0.2)  # 模拟查询时间
        
        # 模拟查询结果
        return [
            {"id": 1, "name": "Alice", "email": "<EMAIL>"},
            {"id": 2, "name": "Bob", "email": "<EMAIL>"}
        ]

async def use_async_context_manager():
    """使用异步上下文管理器"""
    async with AsyncDatabaseConnection("users_db") as db:
        results = await db.query("SELECT * FROM users")
        return results

# ============================================================================
# 4. 异步迭代器
# ============================================================================

class AsyncNumberGenerator:
    """异步数字生成器"""
    
    def __init__(self, max_num: int):
        self.max_num = max_num
        self.current = 0
    
    def __aiter__(self):
        return self
    
    async def __anext__(self):
        if self.current >= self.max_num:
            raise StopAsyncIteration
        
        await asyncio.sleep(0.1)  # 模拟异步操作
        self.current += 1
        return self.current

async def use_async_iterator():
    """使用异步迭代器"""
    print("=== 异步迭代器示例 ===")
    async for number in AsyncNumberGenerator(5):
        print(f"生成的数字: {number}")

# ============================================================================
# 5. 异步 HTTP 请求示例
# ============================================================================

async def fetch_url(session: aiohttp.ClientSession, url: str) -> Dict[str, Any]:
    """异步获取 URL 内容"""
    try:
        async with session.get(url) as response:
            return {
                "url": url,
                "status": response.status,
                "content_length": len(await response.text())
            }
    except Exception as e:
        return {
            "url": url,
            "error": str(e)
        }

async def fetch_multiple_urls(urls: List[str]) -> List[Dict[str, Any]]:
    """并发获取多个 URL"""
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url(session, url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results

# ============================================================================
# 6. 异步文件操作
# ============================================================================

async def write_async_file(filename: str, content: str) -> None:
    """异步写入文件"""
    async with aiofiles.open(filename, 'w', encoding='utf-8') as f:
        await f.write(content)
    print(f"异步写入文件 {filename} 完成")

async def read_async_file(filename: str) -> str:
    """异步读取文件"""
    try:
        async with aiofiles.open(filename, 'r', encoding='utf-8') as f:
            content = await f.read()
        print(f"异步读取文件 {filename} 完成")
        return content
    except FileNotFoundError:
        return "文件不存在"

# ============================================================================
# 7. 错误处理和超时
# ============================================================================

async def task_with_timeout(duration: float) -> str:
    """可能超时的任务"""
    await asyncio.sleep(duration)
    return f"任务完成，耗时 {duration} 秒"

async def handle_timeout_example():
    """处理超时示例"""
    try:
        # 设置 2 秒超时
        result = await asyncio.wait_for(task_with_timeout(1.0), timeout=2.0)
        print(f"任务成功: {result}")
    except asyncio.TimeoutError:
        print("任务超时")

async def handle_exceptions_example():
    """异常处理示例"""
    async def failing_task():
        await asyncio.sleep(0.1)
        raise ValueError("模拟错误")
    
    try:
        await failing_task()
    except ValueError as e:
        print(f"捕获异常: {e}")

# ============================================================================
# 8. 实际应用示例
# ============================================================================

async def simulate_api_call(endpoint: str, delay: float = 0.5) -> Dict[str, Any]:
    """模拟 API 调用"""
    print(f"调用 API: {endpoint}")
    await asyncio.sleep(delay)
    
    return {
        "endpoint": endpoint,
        "status": "success",
        "data": {"message": f"来自 {endpoint} 的数据"},
        "timestamp": datetime.now().isoformat()
    }

async def process_user_data_async(user_id: int) -> Dict[str, Any]:
    """异步处理用户数据"""
    # 并发执行多个 API 调用
    user_info_task = simulate_api_call(f"/users/{user_id}")
    user_posts_task = simulate_api_call(f"/users/{user_id}/posts")
    user_friends_task = simulate_api_call(f"/users/{user_id}/friends")
    
    # 等待所有任务完成
    user_info, user_posts, user_friends = await asyncio.gather(
        user_info_task,
        user_posts_task,
        user_friends_task
    )
    
    return {
        "user_info": user_info,
        "posts": user_posts,
        "friends": user_friends
    }

# ============================================================================
# 9. 主函数和测试
# ============================================================================

async def main():
    """主异步函数"""
    print("异步编程概念学习和练习")
    print("=" * 50)
    
    # 1. 同步 vs 异步对比
    run_sync_tasks()
    await run_async_tasks()
    
    # 2. 简单异步函数
    print("\n=== 简单异步函数 ===")
    result = await simple_async_function()
    print(result)
    
    param_result = await async_function_with_params("测试", 0.2)
    print(f"带参数的异步函数结果: {param_result}")
    
    # 3. 异步上下文管理器
    print("\n=== 异步上下文管理器 ===")
    db_results = await use_async_context_manager()
    print(f"数据库查询结果: {db_results}")
    
    # 4. 异步迭代器
    await use_async_iterator()
    
    # 5. 超时和异常处理
    print("\n=== 超时和异常处理 ===")
    await handle_timeout_example()
    await handle_exceptions_example()
    
    # 6. 文件操作
    print("\n=== 异步文件操作 ===")
    await write_async_file("test_async.txt", "这是异步写入的内容")
    content = await read_async_file("test_async.txt")
    print(f"读取的内容: {content}")
    
    # 7. 实际应用示例
    print("\n=== 实际应用示例 ===")
    user_data = await process_user_data_async(123)
    print(f"用户数据处理完成: {len(user_data)} 个部分")
    
    print("\n" + "=" * 50)
    print("异步编程概念学习完成！")
    print("现在您已经准备好学习 FastAPI 的异步特性了。")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
